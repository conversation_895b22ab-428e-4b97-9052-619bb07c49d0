<template>
  <div class="h-screen-50 overflow-hidden mx-2">
    <!-- Header Section -->
    <TimekeepingHeader />

    <!-- Main Content -->
    <div class="h-[calc(100%-80px)] overflow-y-auto">
      <div class="mx-auto my-4">
        <div v-if="loading" class="flex justify-center items-center py-20">
          <LoadingSpinner />
        </div>

        <div v-else class="space-y-6">
          <!-- Top Section: User Profile + Stats -->
          <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-6">
            <!-- User Profile Card -->
            <div class="lg:col-span-1">
              <UserProfileCard :auth="auth" @check-in="handleCreateCheckin" />
            </div>

            <!-- Stats Cards -->
            <div class="lg:col-span-3 space-y-4">
              <StatsCards
                :present-count="presentCount"
                :absent-count="absentCount"
                :late-count="lateCount"
              />
              <div class="">
                <EmployeeTable :employees="employeeList" />
              </div>
            </div>
          </div>

          <!-- Weekly Schedule -->
          <WeeklySchedule />

          <!-- Time Keeping History -->
          <TimekeepingHistory
            ref="historyRef"
            :data-checkin="timeKeepingStore.dataTimeKeeping"
            :is-manager="isManager"
            :loading-more="loadingMore"
            :has-more="hasMore"
          />
        </div>
      </div>
    </div>

    <!-- Modal -->
    <ModalTimeKeeping v-if="isCloseModel" @isClose="isClose" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import type { Auth } from "~/types/Auth";
// Meta
useHead({
  title: "Chấm công",
  meta: [
    {
      name: "description",
      content: "timekeeping",
    },
  ],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  permission: [
    "SALE_OP",
    "SUPP_ADMIN",
    "ORG_ADMIN",
    "SALE_MANAGER",
    "SUPP_OP",
    "SALE",
    "SALES",
  ],
  name: "Chấm công",
});

// Reactive state
const loading = ref(false);
const loadingMore = ref(false);
const isCloseModel = ref(false);
const hasMore = ref(true);
const historyRef = ref(null);

// Stores and composables
const timeKeepingStore = useTimeKeepingStore();

// Auth
const auth = useCookie("auth").value as unknown as Auth;

// Check role for manager permissions
const isManager = ref<boolean>(false);
const rolesToCheck = [
  "SALE_OP",
  "SUPP_ADMIN",
  "ORG_ADMIN",
  "SALE_MANAGER",
  "SUPP_OP",
];

const checkUserRoles = () => {
  if (auth?.user?.roles && Array.isArray(auth.user.roles)) {
    auth.user.roles.forEach((role) => {
      if (rolesToCheck.includes(role)) {
        isManager.value = true;
      }
    });
  } else {
    isManager.value = false;
  }
};

// Stats computed properties (mock data - replace with real data)
const presentCount = computed(() => 3);
const absentCount = computed(() => 1);
const lateCount = computed(() => 3);

// Employee list (mock data matching the reference image)
const employeeList = computed(() => [
  {
    initial: "N",
    name: "Nguyễn Văn A",
    checkIn: "09:08",
    checkOut: "09:08",
    workHours: "4h 30m",
    status: "Đã về",
  },
  {
    initial: "T",
    name: "Trần Thị B",
    checkIn: "08:45",
    checkOut: "17:30",
    workHours: "8h 45m",
    status: "Đã về",
  },
  {
    initial: "L",
    name: "Lê Văn C",
    checkIn: "09:15",
    checkOut: "--",
    workHours: "3h 45m",
    status: "Đang làm việc",
  },
  {
    initial: "P",
    name: "Phạm Thị D",
    checkIn: "--",
    checkOut: "--",
    workHours: "--",
    status: "Vắng mặt",
  },
]);

// Functions
const handleCreateCheckin = () => {
  isCloseModel.value = true;
};

const isClose = () => {
  isCloseModel.value = false;
};

// Initialize data on mount
onMounted(async () => {
  loading.value = true;
  try {
    checkUserRoles();
    // Initialize timekeeping data here if needed
    // You can add specific timekeeping data loading here
  } catch (error) {
    console.error("Error loading timekeeping data:", error);
  } finally {
    loading.value = false;
  }
});
</script>
