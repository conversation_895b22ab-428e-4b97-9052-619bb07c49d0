<template>
  <div
    class="campaign-wrapper bg-white border border-gray-100 rounded-lg shadow-sm contain-layout will-change-contents"
  >
    <div v-if="campaign?.length > 0" class="p-4">
      <!-- Header with icon and count -->
      <div class="flex items-center gap-2 mb-3">
        <div
          class="flex items-center justify-center w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"
        >
          <svg
            class="w-3 h-3 text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fill-rule="evenodd"
              d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z"
              clip-rule="evenodd"
            ></path>
          </svg>
        </div>
        <h3 class="font-semibold text-gray-800 text-sm">Sự kiện khuyến mãi</h3>
        <div class="ml-auto">
          <span
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
          >
            {{ campaign.length }} sự kiện
          </span>
        </div>
      </div>

      <!-- Campaign items with improved styling -->
      <div
        id="campaign-scroll"
        class="flex gap-2 overflow-x-auto scrollbar-thin pb-1"
      >
        <button
          v-for="(item, index) in campaign"
          :key="item?.campaignId"
          @click="selectedCampaign(item)"
          class="group relative inline-flex items-center px-3 py-2 text-xs font-medium rounded-lg border border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 text-gray-700 hover:from-blue-50 hover:to-blue-100 hover:text-blue-700 hover:border-blue-200 focus:ring-offset-1 transition-all duration-200 transform hover:shadow-md flex-shrink-0 min-w-[120px]"
        >
          <!-- Campaign icon -->
          <div
            class="flex items-center justify-center w-4 h-4 mr-2 rounded-full bg-gradient-to-r from-orange-400 to-pink-500 group-hover:from-blue-400 group-hover:to-blue-600 transition-all duration-200"
          >
            <svg
              class="w-2 h-2 text-white"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
              ></path>
            </svg>
          </div>

          <!-- Campaign name with truncation -->
          <span class="truncate max-w-[100px]" :title="item.campaignActionName">
            {{ item.campaignActionName }}
          </span>

          <!-- Hover effect indicator -->
          <div
            class="absolute inset-0 rounded-lg bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          ></div>
        </button>
      </div>
    </div>

    <!-- Empty state -->
    <div v-else class="p-6 text-center">
      <div class="flex flex-col items-center justify-center">
        <div
          class="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-full mb-3"
        >
          <svg
            class="w-6 h-6 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
        </div>
        <p class="text-sm text-gray-500 font-medium">Không có sự kiện nào</p>
        <p class="text-xs text-gray-400 mt-1">
          Hiện tại chưa có chương trình khuyến mãi
        </p>
      </div>
    </div>
  </div>

  <CampaignMessage
    v-if="isOpenPopup"
    @confirm="handleToogle"
    @cancel="handleToogle"
    :campaignInfo="campaignInfo"
  />
</template>

<script setup lang="ts">
const CampaignMessage = defineAsyncComponent(
  () => import("~/components/dialog/CampaignMessage.vue")
);

const orderStore = useOrderStore();
const campaign = computed(() => orderStore.campaign);
const isOpenPopup = ref(false);
const campaignInfo = ref();

const selectedCampaign = async (item: any) => {
  campaignInfo.value = item;
  isOpenPopup.value = true;
};

const handleToogle = () => {
  isOpenPopup.value = !isOpenPopup.value;
};
</script>
