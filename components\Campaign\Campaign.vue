<template>
  <div class="bg-white px-2 rounded contain-layout will-change-contents">
    <div v-if="campaign?.length > 0">
      <div class="font-bold text-primary my-1">Sự kiện</div>
      <div id="campaign-scroll" class="flex overflow-x-auto whitespace-nowrap">
        <span
          v-for="(item, index) in campaign"
          :key="item?.campaignId"
          @click="selectedCampaign(item)"
          class="inline-block bg-slate-200 p-1 rounded px-1 mb-2 mr-3 cursor-pointer hover:bg-slate-300 transition-colors duration-200 min-w-[80px] text-center flex-shrink-0"
        >
          {{ item.campaignActionName }}
        </span>
      </div>
    </div>
  </div>
  <CampaignMessage
    v-if="isOpenPopup"
    @confirm="handleToogle"
    @cancel="handleToogle"
    :campaignInfo="campaignInfo"
  />
</template>

<script setup lang="ts">
import { onMounted, ref, watch, computed, nextTick } from "vue";
const CampaignMessage = defineAsyncComponent(
  () => import("~/components/dialog/CampaignMessage.vue")
);
const orderStore = useOrderStore();
const campaign = computed(() => orderStore.campaign);
const isOpenPopup = ref(false);
const campaignInfo = ref();
const readyToRender = ref(false);
const scrollContainer = ref<HTMLDivElement | null>(null);

const selectedCampaign = async (item: any) => {
  campaignInfo.value = item;
  isOpenPopup.value = true;
};

const handleToogle = () => {
  isOpenPopup.value = !isOpenPopup.value;
};
</script>
