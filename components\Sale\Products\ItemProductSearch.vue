<template>
  <div class="flex">
    <div class="w-[50px] h-[50px] mr-[10px] relative">
      <NuxtImg
        :src="handleGetImageProductUrl()"
        alt="product"
        class="object-contain w-full h-full rounded-md"
        loading="lazy"
        preload
      />
    </div>
    <div class="w-[80%] text-black text-sm space-y-1">
      <div class="font-semibold">
        {{
          `${product.title} (#${product?.id}${
            product?.sku ? ` - SKU: ${product?.sku}` : ``
          })`
        }}
      </div>
      <div class="flex items-center">
        <div class="font-semibold text-red-500">
          {{
            product.price !== null && product.price !== undefined
              ? formatCurrency(product.price)
              : "Chưa có giá"
          }}
        </div>
        <div class="text-[12px] text-gray-300 line-through ml-2">
          {{
            product.compareAtPrice ? formatCurrency(product.compareAtPrice) : ""
          }}
        </div>
      </div>
      <!-- VAT display -->
      <div class="flex items-center text-xs">
        <span class="text-primary">VAT: </span>
        <span class="ml-1">{{ product.vat || 0 }}%</span>
        <span class="ml-2 text-primary">Giá sau VAT: </span>
        <span class="ml-1 font-semibold">{{
          formatCurrency(priceAfterVAT)
        }}</span>
        <span
          v-if="!showInventory && product?.subType === 'SIMPLE'"
          class="ml-1"
          @click.stop="handleViewInventory"
          >xem tồn kho</span
        >
        <span v-if="showInventory" class="ml-1 font-semibold"
          ><span class="text-primary">Tồn kho: </span
          >{{ inventory?.orderAble }}</span
        >
      </div>
      <div class="text-xs">
        <span v-if="promotion">{{ promotion.campaignName }}</span>
        <span v-if="promotion">
          {{
            ` (${formatTimestampV2(promotion.fromDate)}-${formatTimestampV2(
              promotion.toDate
            )})`
          }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps(["product"]);

const { getPromotionProductPrice } = useCampaign();
const promotion = ref<{
  campaignName: string;
  fromDate: string;
  toDate: string;
} | null>(null);

// Calculate price after VAT
const priceAfterVAT = computed(() => {
  if (props.product.price === null || props.product.price === undefined) {
    return 0;
  }

  const vatRate = props.product.vat || 0;
  const vatAmount = (props.product.price * vatRate) / 100;
  return props.product.price + vatAmount;
});

const handlePromotionProductPrice = async () => {
  if (props.product?.compareAtPrice) {
    const response = await getPromotionProductPrice(
      props.product.id,
      props.product.price
    );
    promotion.value = response;
  }
};
const { getImageProducrUrl } = usePortal();
const handleGetImageProductUrl = () => {
  const url = getImageProducrUrl(props.product.id, "PRODUCT");
  return url;
};
onMounted(async () => {
  await handlePromotionProductPrice();
});
const { getInventoryV2 } = useWarehouse();
const warehouseId = useCookie("warehouseId");
const inventory = ref();
const showInventory = ref(false);
const handleViewInventory = async () => {
  console.log("xem tồn kho", props.product);
  showInventory.value = !showInventory.value;
  const data = [
    {
      productId: props.product?.id,
      variantId: "",
      sku: props.product?.sku,
    },
  ];
  const res = await getInventoryV2((warehouseId.value as string) || "", data);
  inventory.value = res[0];
  console.log("res", res);
};
</script>
