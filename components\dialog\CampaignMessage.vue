<template>
  <!-- Backdrop with improved animation -->
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="isVisible"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm p-4"
        @click.self="cancel"
      >
        <!-- Modal with enhanced styling -->
        <Transition
          enter-active-class="transition-all duration-300 ease-out"
          enter-from-class="opacity-0 scale-95 translate-y-4"
          enter-to-class="opacity-100 scale-100 translate-y-0"
          leave-active-class="transition-all duration-200 ease-in"
          leave-from-class="opacity-100 scale-100 translate-y-0"
          leave-to-class="opacity-0 scale-95 translate-y-4"
        >
          <div
            v-if="isVisible"
            class="bg-white rounded-2xl shadow-2xl border border-gray-100 max-w-xl w-full max-h-[90vh] overflow-hidden"
            @click.stop
          >
            <!-- Header with gradient background -->
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-4">
              <div class="flex items-center justify-between">
                <!-- Campaign icon -->
                <div class="flex items-center gap-3">
                  <div
                    class="flex items-center justify-center w-8 h-8 bg-white/20 rounded-full backdrop-blur-sm"
                  >
                    <svg
                      class="w-4 h-4 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                  </div>
                  <h2 class="text-lg font-semibold text-white">
                    Thông tin chiến dịch
                  </h2>
                </div>

                <!-- Close button -->
                <button
                  @click="cancel"
                  class="flex items-center justify-center w-8 h-8 text-white/80 hover:text-white hover:bg-white/20 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50"
                >
                  <svg
                    class="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M6 18L18 6M6 6l12 12"
                    ></path>
                  </svg>
                </button>
              </div>
            </div>

            <!-- Content section -->
            <div class="p-6">
              <!-- Campaign name with icon -->
              <div class="flex items-start gap-3 mb-4">
                <div
                  class="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-orange-400 to-pink-500 rounded-full flex-shrink-0"
                >
                  <svg
                    class="w-5 h-5 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                    ></path>
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <h3
                    class="text-lg font-semibold text-gray-900 mb-1 break-words"
                  >
                    {{ campaignInfo.campaignActionName }}
                  </h3>
                  <p class="text-sm text-gray-500">
                    Chương trình khuyến mãi đặc biệt
                  </p>
                </div>
              </div>

              <!-- Date range with enhanced styling -->
              <div class="bg-gray-50 rounded-xl p-4 mb-4">
                <div class="flex items-center gap-2 mb-2">
                  <svg
                    class="w-4 h-4 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                    ></path>
                  </svg>
                  <span class="text-sm font-medium text-gray-700"
                    >Thời gian áp dụng</span
                  >
                </div>
                <div class="flex items-center justify-between text-sm">
                  <div class="flex flex-col">
                    <span class="text-gray-500">Từ ngày</span>
                    <span class="font-medium text-gray-900">{{
                      formatTimestampV2(campaignInfo.fromDate)
                    }}</span>
                  </div>
                  <div class="flex items-center px-2">
                    <svg
                      class="w-4 h-4 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 5l7 7-7 7"
                      ></path>
                    </svg>
                  </div>
                  <div class="flex flex-col text-right">
                    <span class="text-gray-500">Đến ngày</span>
                    <span class="font-medium text-gray-900">{{
                      formatTimestampV2(campaignInfo.toDate)
                    }}</span>
                  </div>
                </div>
              </div>

              <!-- Status indicator -->
              <div
                class="flex items-center justify-center gap-2 p-3 bg-green-50 border border-green-200 rounded-xl"
              >
                <div
                  class="w-2 h-2 bg-green-500 rounded-full animate-pulse"
                ></div>
                <span class="text-sm font-medium text-green-700"
                  >Đang hoạt động</span
                >
              </div>
            </div>

            <!-- Footer with action buttons -->
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
interface CampaignInfo {
  campaignActionName: string;
  fromDate: string | number;
  toDate: string | number;
  campaignId?: string;
  description?: string;
}

interface Props {
  campaignInfo: CampaignInfo;
}

const emit = defineEmits<{
  confirm: [];
  cancel: [];
  viewDetails: [campaignInfo: CampaignInfo];
}>();

const props = defineProps<Props>();

const isVisible = ref(true);

// Animation and lifecycle management
const cancel = () => {
  isVisible.value = false;
  // Delay emit to allow animation to complete
  setTimeout(() => {
    emit("cancel");
  }, 200);
};

const handleViewDetails = () => {
  emit("viewDetails", props.campaignInfo);
  cancel();
};

// Auto-close after 10 seconds (optional)
const autoCloseTimer = ref<NodeJS.Timeout | null>(null);

onMounted(() => {
  // Auto-close after 10 seconds if user doesn't interact
  autoCloseTimer.value = setTimeout(() => {
    if (isVisible.value) {
      cancel();
    }
  }, 10000);
});

onUnmounted(() => {
  if (autoCloseTimer.value) {
    clearTimeout(autoCloseTimer.value);
  }
});

// Keyboard event handling
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Escape") {
    cancel();
  }
};

onMounted(() => {
  document.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeydown);
});
</script>
